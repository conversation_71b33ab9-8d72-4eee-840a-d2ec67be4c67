
import type { Meta, StoryObj } from '@storybook/react';
import Modal from './Modal';
import React from 'react';



const meta: Meta<typeof Modal> = {
  title: 'General/Blocks/Modal',
  component: Modal,
  tags: ['autodocs', '!dev'],
  args: {
    title: 'Title',
    body: 'Subtitle',
    cancelText: '<PERSON>chazar',
    confirmText: 'Aceptar'
  },
  argTypes: {
    title: { control: 'text' },
    body: {
      control: 'text',
      table: {
        type: { summary: 'string | ReactNode' },
      },
      description: 'Contenido del modal. Puede ser texto simple o elementos JSX.'
    },
    cancelText: { control: 'text' },
    confirmText: { control: 'text' },
    contentChildren: {
      control: false,
      table: {
        type: { summary: 'ReactNode' },
      },
    },
  },
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'Modal de pantalla completa que bloquea el uso de la aplicación hasta que el usuario realiza una acción.',
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Modal>;

export const Default: Story = {
  name: 'Default Modal',
};

export const WithJSXBody: Story = {
  name: 'Modal with JSX Body',
  args: {
    title: 'Modal con contenido JSX',
    body: (
      <div>
        <p>Este modal contiene <strong>contenido JSX</strong> en lugar de texto simple.</p>
        <ul style={{ marginTop: '16px', paddingLeft: '20px' }}>
          <li>Elemento de lista 1</li>
          <li>Elemento de lista 2</li>
          <li>Elemento de lista 3</li>
        </ul>
        <p style={{ marginTop: '16px', fontStyle: 'italic' }}>
          Puedes incluir cualquier elemento React válido.
        </p>
      </div>
    ),
    cancelText: 'Cancelar',
    confirmText: 'Entendido'
  },
};
