@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';

.modal {
    &-fullscreen-wrapper {
      position: relative;
      width: 100%;
      height: 100dvh;
      display: flex;
      align-self: center;
      justify-self: center;
      padding: 1rem;
    }
    &-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #081f2ecc;
    }
    &-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
      border-radius: radius.$radius-container;
      max-width: 800px;
      width: 100%;
      max-height: calc(100dvh - 64px);
      margin: auto;
      padding: 2.5rem 1.5rem 0 1.5rem;
      background-color: colors.$background;
      box-sizing: border-box;

      &.top-padding {
        padding-top: 4rem;
      }
      
      @media (max-width: 1024px) {
        padding: 3rem 1rem 0 1rem;
      }

      @media (max-width: 600px) {
        height: 100%;
      }
  
      .button-close {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
    &-header {
      flex-shrink: 0; 
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      width: 100%;
    }
    &-content {
      flex-grow: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 2rem;
      width: 100%;
    }
    &-footer {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: 100%;
      padding: 1.5rem 0;
      gap: 1rem;
      @media (max-width: 1024px) {
        padding: 1rem 0;
      }
  
      @media (max-width: 600px) {
        flex-direction: column;
  
        .button-primary,
        .button-secondary {
          width: 100%;
        }
      }
    }
  }
  