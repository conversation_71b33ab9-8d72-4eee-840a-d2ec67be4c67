import React, { FC, useState } from 'react';
import './PrimaryButton.scss';
import Spinner from '../Spinner/Spinner';

interface PrimaryButtonProps {
	onClick: () => void;
	isDisabled?: boolean;
	isLoading?: boolean;
	text: string;
	leftIcon?: React.ReactNode;
	ariaLabel?: string;
	backgroundColor?: string;
	textColor?: string;
	borderRadius?: string;
	spinnerColor?: string;
	className?: string;
	id?:string
}

const PrimaryButton: FC<PrimaryButtonProps> = ({
	onClick,
	isDisabled = false,
	isLoading = false,
	text,
	leftIcon,
	backgroundColor,
	textColor,
	borderRadius,
	spinnerColor,
	className,
	ariaLabel,
	id
}) => {
	const [isClicked, setIsClicked] = useState(false);

	const handleClick = () => {
		if (!isDisabled && !isLoading) {
			setIsClicked(true);
			onClick();
			setTimeout(() => setIsClicked(false), 200);
		}
	};

	return (
		<button
			type="button"
			onClick={handleClick}
			className={`${className ?? ''} button-primary label1 ${isDisabled ? 'disabled' : ''} ${isLoading ? 'loading' : ''} ${isClicked ? ' clicked' : ''}`}
			disabled={isDisabled || isLoading}
			style={{
				backgroundColor,
				color: textColor,
				borderRadius: borderRadius,
				position: 'relative',
			}}
			aria-label={ariaLabel}
			id={id}
		>
			<span className="button-content" style={{ opacity: isLoading ? 0 : 1 }}>
				{leftIcon}
				{text}
			</span>

			{isLoading && (
				<div className="button-spinner-wrapper">
					<Spinner color={spinnerColor} size="small" />
				</div>
			)}
		</button>
	);
};

export default PrimaryButton;
