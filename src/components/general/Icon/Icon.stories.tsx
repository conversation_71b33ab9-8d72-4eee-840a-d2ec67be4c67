import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Icon, IconType } from "./Icons";

const meta: Meta<typeof Icon> = {
  title: "General/Others/Icon",
  component: Icon,
  tags: ["autodocs", "!dev"],
  args: {
    type: "play",
    color: "#ffffff",
  },
  argTypes: {
    color: {
      control: "color",
    },
    type: {
      control: {
        type: "select",
        options: [
          "home",
          "play",
          "pause",
          "musicOn",
          "musicOff",
          "soundOn",
          "soundOff",
          "aura",
          "reload",
          "mic",
          "menu",
          "thumbUp",
          "thumbDown",
          "thumbUpFilled",
          "thumbDownFilled",
          "close",
          "addMore",
          "back",
        ] as IconType[],
      },
    },
  },
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark-blue",
      values: [{ name: "dark-blue", value: "#061824" }],
    },
    docs: {
      description: {
        component: "Iconos predefinidos.",
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Icon>;

export const Default: Story = {
  name: "Icon",
};
