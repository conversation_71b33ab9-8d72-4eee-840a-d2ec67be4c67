import React, { FC, JSX } from "react";
import "./Icon.scss";

export type IconType =
  | "home"
  | "play"
  | "pause"
  | "musicOn"
  | "musicOff"
  | "soundOn"
  | "soundOff"
  | "aura"
  | "reload"
  | "mic"
  | "menu"
  | "thumbUp"
  | "thumbDown"
  | "thumbUpFilled"
  | "thumbDownFilled"
  | "close"
  | "addMore"
  | "back"
  | "star"
  | "starFilled"
  | "next"
  | "checkCircle";

interface IconProps {
  type: IconType;
  color?: string;
}

export const Icon: FC<IconProps> = ({ type, color }) => {
  const icons: Record<IconType, JSX.Element> = {
    home: <HomeIcon color={color} />,
    play: <PlayIcon color={color} />,
    pause: <PauseIcon color={color} />,
    musicOn: <MusicIcon color={color} />,
    musicOff: <MusicOffIcon color={color} />,
    soundOn: <SoundOn color={color} />,
    soundOff: <SoundOff color={color} />,
    aura: <AuraIcon color={color} />,
    reload: <ReloadIcon color={color} />,
    mic: <MicIcon color={color} />,
    menu: <MenuIcon color={color} />,
    thumbUp: <ThumbUp color={color} />,
    thumbDown: <ThumbDown color={color} />,
    thumbUpFilled: <ThumbUpFilled color={color} />,
    thumbDownFilled: <ThumbDownFilled color={color} />,
    close: <Close color={color} />,
    addMore: <AddMore color={color} />,
    back: <Back color={color} />,
    star: <Star color={color} />,
    starFilled: <StarFilled color={color} />,
    next: <NextIcon color={color} />,
    checkCircle: <CheckCircle color={color} />,
  };

  return icons[type] || null;
};

export default Icon;

const HomeIcon = ({ color }: { color?: string }) => (
  <svg
    className="icon home"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    fill={color}
  >
    <path d="M12 3L2 12H5V21H10V16H14V21H19V12H22L12 3Z" fill={color} />
  </svg>
);

const PlayIcon = ({ color }: { color?: string }) => (
  <svg
    className="icon play"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    fill={color}
  >
    <path
      d="M6.00018 4.97066C6.00018 4.3186 6.70452 3.90993 7.27069 4.23336L19.5724 11.2627C20.1429 11.5887 20.1429 12.4112 19.5724 12.7373L7.27069 19.7666C6.70451 20.0901 6.00018 19.6814 6.00018 19.0293V4.97066Z"
      fill={color}
    />
  </svg>
);

const PauseIcon = ({ color }: { color?: string }) => (
  <svg
    className="icon pause"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    fill={color}
  >
    <path
      d="M7.33566 4C8.1946 4.00006 8.89112 4.71617 8.89133 5.59961V18.4004C8.89112 19.2838 8.1946 19.9999 7.33566 20C6.47668 20 5.7802 19.2839 5.78 18.4004V5.59961C5.7802 4.71613 6.47668 4 7.33566 4ZM16.6687 4C17.5277 4 18.2241 4.71613 18.2243 5.59961V18.4004C18.2241 19.2839 17.5277 20 16.6687 20C15.8098 19.9999 15.1132 19.2838 15.113 18.4004V5.59961C15.1132 4.71621 15.8098 4.00012 16.6687 4Z"
      fill={color}
    />
  </svg>
);

const MusicIcon = ({ color }: { color?: string }) => (
  <svg
    className="icon music"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 -960 960 960"
    fill={color}
  >
    <path
      d="M400-120q-66 0-113-47t-47-113q0-66 47-113t113-47q23 0 42.5 5.5T480-418v-422h240v160H560v400q0 66-47 113t-113 47Z"
      fill={color}
    />
  </svg>
);

const MusicOffIcon = ({ color }: { color?: string }) => (
  <svg
    className="icon musicOff"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 -960 960 960"
    fill={color}
  >
    <path
      d="M792-56 56-792l56-56 736 736-56 56ZM560-514l-80-80v-246h240v160H560v166ZM400-120q-66 0-113-47t-47-113q0-66 47-113t113-47q23 0 42.5 5.5T480-418v-62l80 80v120q0 66-47 113t-113 47Z"
      fill={color}
    />
  </svg>
);

const SoundOn = ({ color }: { color?: string }) => (
  <svg className="icon soundOn" viewBox="0 0 32 32" fill={color}>
    <path
      d="M17.6616 5.15217C17.3328 4.95376 16.9337 4.95002 16.5975 5.13719L6.98687 10.5915H4.06045C3.47665 10.5915 3 11.0745 3 11.6659V19.3365C3 19.928 3.47665 20.4109 4.06045 20.4109H6.98687L16.5679 25.8465C16.7379 25.9476 16.9263 26 17.1185 26C17.3106 26 17.4769 25.9551 17.6395 25.8615C17.9794 25.6706 18.19 25.3074 18.19 24.9106V6.08805C18.19 5.69873 17.9942 5.35058 17.6616 5.15217Z"
      fill={color}
    />
    <path
      d="M22.1973 21.3529C21.9701 21.3529 21.7467 21.2528 21.5966 21.0641C21.3309 20.7329 21.3848 20.2477 21.7159 19.9821C22.9944 18.9578 23.726 17.4329 23.726 15.8002C23.726 14.1675 22.9944 12.6426 21.7159 11.6183C21.3848 11.3526 21.3309 10.8674 21.5966 10.5363C21.8623 10.2051 22.3475 10.1512 22.6786 10.4169C24.3229 11.7338 25.2663 13.6977 25.2663 15.8002C25.2663 17.9027 24.3229 19.8665 22.6786 21.1835C22.5361 21.299 22.3667 21.3529 22.1973 21.3529Z"
      fill={color}
    />
    <path
      d="M24.2637 24.826C24.0365 24.826 23.8132 24.7258 23.663 24.5372C23.3973 24.206 23.4512 23.7208 23.7823 23.4551C26.1197 21.5798 27.4598 18.7919 27.4598 15.7999C27.4598 12.8079 26.1197 10.0161 23.7823 8.14467C23.4512 7.87897 23.3973 7.39378 23.663 7.06262C23.9287 6.73146 24.4139 6.67755 24.745 6.94325C27.4482 9.1112 29.0001 12.3381 29.0001 15.7999C29.0001 19.2617 27.4482 22.4886 24.745 24.6565C24.6026 24.772 24.4331 24.826 24.2637 24.826Z"
      fill={color}
    />
  </svg>
);

const SoundOff = ({ color }: { color?: string }) => (
  <svg className="icon soundOff" viewBox="0 0 32 32" fill={color}>
    <path
      d="M17.6613 5.15217C17.3325 4.95376 16.9334 4.95002 16.5972 5.13719L6.98678 10.5915H4.06043C3.47664 10.5915 3 11.0745 3 11.6659V19.3365C3 19.928 3.47664 20.4109 4.06043 20.4109H6.98678L16.5676 25.8465C16.7376 25.9476 16.926 26 17.1182 26C17.3103 26 17.4766 25.9551 17.6391 25.8615C17.9791 25.6706 18.1897 25.3074 18.1897 24.9106V6.08805C18.1897 5.69873 17.9939 5.35058 17.6613 5.15217Z"
      fill={color}
    />
    <path
      d="M26.9011 15.4998L30.3447 12.0108C30.6292 11.7225 30.6292 11.2583 30.3447 10.9701C30.0602 10.6818 29.602 10.6818 29.3175 10.9701L25.8739 14.4591L22.4302 10.9701C22.1457 10.6818 21.6876 10.6818 21.4031 10.9701C21.1186 11.2583 21.1186 11.7225 21.4031 12.0108L24.8467 15.4998L21.4031 18.9888C21.1186 19.277 21.1186 19.7412 21.4031 20.0295C21.5435 20.1717 21.7319 20.2466 21.9167 20.2466C22.1014 20.2466 22.2898 20.1755 22.4302 20.0295L25.8739 16.5405L29.3175 20.0295C29.4579 20.1717 29.6464 20.2466 29.8311 20.2466C30.0158 20.2466 30.2043 20.1755 30.3447 20.0295C30.6292 19.7412 30.6292 19.277 30.3447 18.9888Z"
      fill={color}
    />
  </svg>
);

const MenuIcon = ({ color }: { color?: string }) => (
  <svg className="icon menu" viewBox="0 0 32 32" fill={color}>
    <path
      d="M17.9998 8C17.9998 9.10457 17.1043 10 15.9998 10C14.8952 10 13.9998 9.10457 13.9998 8C13.9998 6.89543 14.8952 6 15.9998 6C17.1043 6 17.9998 6.89543 17.9998 8Z"
      fill={color}
    />
    <path
      d="M17.9998 16C17.9998 17.1046 17.1043 18 15.9998 18C14.8952 18 13.9998 17.1046 13.9998 16C13.9998 14.8954 14.8952 14 15.9998 14C17.1043 14 17.9998 14.8954 17.9998 16Z"
      fill={color}
    />
    <path
      d="M15.9998 26C17.1043 26 17.9998 25.1046 17.9998 24C17.9998 22.8954 17.1043 22 15.9998 22C14.8952 22 13.9998 22.8954 13.9998 24C13.9998 25.1046 14.8952 26 15.9998 26Z"
      fill={color}
    />
  </svg>
);

const MicIcon = ({ color }: { color?: string }) => (
  <svg className="icon mic" viewBox="0 0 32 32" fill={color}>
    <path
      d="M21.7638 8.70502C21.7638 4.94404 19.7171 2.87122 15.9972 2.87122C12.2774 2.87122 10.2269 4.94404 10.2269 8.70502V17.7582C10.2269 21.5192 12.2736 23.592 15.9935 23.592C19.7171 23.592 21.7638 21.5192 21.7638 17.7582V8.70502ZM17.3007 27.8311C17.3007 27.1177 16.7293 26.5426 16.0271 26.5426C15.325 26.5426 14.7535 27.1177 14.7535 27.8311C14.7498 28.5369 15.3212 29.1158 16.0271 29.1196C16.7293 29.1196 17.3007 28.5407 17.3007 27.8311ZM20.4641 25.254C21.1662 25.254 21.7377 25.8292 21.7377 26.5426C21.7377 27.2522 21.1662 27.8311 20.4641 27.8311C19.7582 27.8273 19.1868 27.2522 19.1905 26.5426C19.1905 25.8292 19.7619 25.254 20.4641 25.254ZM25.3007 23.4725C25.3007 22.7592 24.7293 22.184 24.0271 22.184C23.3212 22.184 22.7535 22.7629 22.7535 23.4725C22.7498 24.1784 23.3212 24.7573 24.0271 24.761C24.7293 24.761 25.3007 24.1821 25.3007 23.4725ZM25.3007 17.7993C25.9057 17.8031 26.3913 18.2961 26.3875 18.8974C26.3913 19.5024 25.9057 19.9954 25.3007 19.9991C24.6957 19.9954 24.2101 19.5024 24.2139 18.8974C24.2101 18.2923 24.6957 17.8031 25.3007 17.7993ZM12.804 26.5426C12.804 25.8292 12.2325 25.254 11.5304 25.254C10.8245 25.254 10.2531 25.8292 10.2568 26.5426C10.2531 27.2522 10.8245 27.8273 11.5304 27.8311C12.2325 27.8311 12.804 27.2522 12.804 26.5426ZM7.96737 22.184C8.66951 22.184 9.24094 22.7592 9.24094 23.4725C9.24094 24.1821 8.66951 24.761 7.96737 24.761C7.26148 24.7573 6.69005 24.1784 6.69379 23.4725C6.69379 22.7629 7.26148 22.184 7.96737 22.184ZM6.69379 19.9991C7.29883 19.9954 7.78436 19.5024 7.78062 18.8974C7.78436 18.2923 7.29883 17.8031 6.69379 17.7993C6.08875 17.8031 5.60322 18.2923 5.60695 18.8974C5.60322 19.5024 6.08875 19.9954 6.69379 19.9991Z"
      fill={color}
    />
  </svg>
);

const ReloadIcon = ({ color }: { color?: string }) => (
  <svg className="icon reload" viewBox="0 0 24 24" fill={color}>
    <path
      d="M7.11247 8.67596C7.16567 9.0531 6.91098 9.39871 6.54997 9.45428C6.51967 9.46216 6.48944 9.4621 6.45914 9.4621C6.13629 9.4621 5.85164 9.21248 5.80582 8.86737L5.60368 7.39764C4.71443 8.73947 4.22868 10.3399 4.22868 11.9953C4.22874 16.4733 7.71357 20.1135 12.0002 20.1135C13.178 20.1134 14.31 19.8513 15.3625 19.3195C15.6854 19.1608 16.0852 19.3035 16.2297 19.6408C16.3893 19.9862 16.2517 20.3959 15.9211 20.5666C14.6938 21.1857 13.3677 21.4992 11.9923 21.4992C6.97609 21.4992 2.89863 17.2394 2.8986 11.9992C2.8986 10.0937 3.44512 8.26344 4.4445 6.70331L3.25211 6.88202C2.89109 6.9376 2.5602 6.67127 2.507 6.29413C2.45394 5.91708 2.70856 5.56747 3.0695 5.5119L6.60368 4.98065L7.11247 8.67596ZM12.0002 2.49921C17.0162 2.49946 21.0939 6.7591 21.0939 11.9992C21.0939 13.9127 20.5543 15.7351 19.5548 17.2873L20.7482 17.1086C21.1013 17.0612 21.4389 17.3188 21.4923 17.6955C21.5456 18.0647 21.2911 18.4182 20.9377 18.4777L17.4074 19.0099L16.8986 15.3137C16.8456 14.9366 17.1002 14.5919 17.4611 14.5363C17.8143 14.489 18.1519 14.7464 18.2052 15.1232L18.4064 16.592C19.288 15.2502 19.7746 13.6665 19.7746 12.0031C19.7746 7.52508 16.2897 3.88495 12.0031 3.88495C10.8404 3.88501 9.71522 4.14725 8.67789 4.66327C8.34738 4.82164 7.94506 4.67837 7.79313 4.33319C7.64112 3.98781 7.77794 3.56718 8.10856 3.40839C9.33218 2.80502 10.6397 2.49921 12.0002 2.49921Z"
      fill={color}
    />
  </svg>
);

const AuraIcon = ({ color }: { color?: string }) => (
  <svg className="icon aura" viewBox="0 0 32 32" fill={color}>
    <path
      d="M14.9316 26.6527C15.7404 26.0522 16.8726 26.2352 17.4605 27.0615C18.0484 27.8878 17.8692 29.0445 17.0605 29.645C16.2516 30.2455 15.1194 30.0625 14.5316 29.2363C13.9437 28.41 14.1228 27.2533 14.9316 26.6527ZM7.56143 26.3332C7.89716 25.861 8.5441 25.7562 9.00639 26.0992C9.46868 26.4422 9.57124 27.1031 9.23551 27.5754C8.89979 28.0476 8.25284 28.1524 7.79055 27.8094C7.32827 27.4664 7.22571 26.8055 7.56143 26.3332ZM22.9921 26.0994C23.4543 25.7562 24.1012 25.8608 24.4371 26.3329C24.7731 26.8051 24.6707 27.466 24.2085 27.8092C23.7463 28.1524 23.0994 28.0478 22.7634 27.5757C22.4275 27.1035 22.5299 26.4426 22.9921 26.0994ZM10.1075 20.0586C10.9469 18.8779 12.5641 18.6159 13.7199 19.4734C14.8757 20.3309 15.1321 21.9832 14.2927 23.164C13.4534 24.3446 11.836 24.6067 10.6802 23.7491C9.52455 22.8915 9.26807 21.2393 10.1075 20.0586ZM18.2805 19.4722C19.4359 18.6142 21.0533 18.8756 21.8931 20.0561C22.7329 21.2365 22.4771 22.8889 21.3216 23.7468C20.1662 24.6048 18.5487 24.3434 17.7089 23.1629C16.8691 21.9826 17.125 20.3301 18.2805 19.4722ZM26.5004 18.1826C27.4512 17.8668 28.4727 18.3983 28.7819 19.3697C29.091 20.3411 28.5707 21.3846 27.6199 21.7005C26.669 22.0163 25.6476 21.4849 25.3385 20.5134C25.0293 19.5419 25.5495 18.4985 26.5004 18.1826ZM3.47172 18.8482C4.05929 18.0217 5.19145 17.8383 6.00043 18.4385C6.8094 19.0388 6.98892 20.1954 6.40136 21.0219C5.81379 21.8484 4.68174 22.0318 3.87266 21.4315C3.06368 20.8313 2.88416 19.6746 3.47172 18.8482ZM7.38978 13.4105C7.83092 12.0227 9.28991 11.2629 10.6484 11.7136C12.0069 12.1643 12.7506 13.6548 12.3095 15.0427C11.8683 16.4305 10.4094 17.1904 9.05091 16.7396C7.69231 16.2889 6.94863 14.7985 7.38978 13.4105ZM21.3485 11.711C22.707 11.2598 24.1661 12.0191 24.6078 13.4068C25.0494 14.7946 24.3062 16.2853 22.9478 16.7365C21.5894 17.1877 20.1302 16.4284 19.6886 15.0407C19.247 13.6529 19.9901 12.1622 21.3485 11.711ZM2.71687 11.8576C2.89333 11.3024 3.47694 10.9985 4.02034 11.1788C4.56375 11.3591 4.86124 11.9552 4.68478 12.5103C4.50832 13.0656 3.92471 13.3695 3.3813 13.1891C2.8379 13.0089 2.54041 12.4127 2.71687 11.8576ZM27.978 11.1788C28.5214 10.9984 29.1051 11.3021 29.2817 11.8572C29.4584 12.4123 29.1612 13.0086 28.6178 13.1891C28.0744 13.3696 27.4907 13.0658 27.3141 12.5108C27.1374 11.9556 27.4347 11.3594 27.978 11.1788ZM15.9974 7.01869C17.4258 7.01839 18.584 8.20116 18.5842 9.6604C18.5844 11.1197 17.4268 12.3028 15.9984 12.3031C14.57 12.3033 13.4118 11.1205 13.4116 9.66131C13.4114 8.20207 14.5691 7.019 15.9974 7.01869ZM22.8383 4.80574C23.8382 4.80553 24.649 5.63351 24.6491 6.65499C24.6493 7.67647 23.8388 8.50465 22.8389 8.50485C21.8392 8.50505 21.0284 7.67707 21.0283 6.6556C21.0281 5.63412 21.8385 4.80594 22.8383 4.80574ZM7.44366 6.08007C7.75242 5.10845 8.77372 4.57672 9.72467 4.89215C10.6756 5.20757 11.1963 6.25095 10.8875 7.22246C10.5786 8.19397 9.5574 8.72581 8.60635 8.41038C7.65539 8.09486 7.13481 7.05158 7.44366 6.08007ZM15.9991 1.99854C16.5706 1.99843 17.0338 2.47152 17.0339 3.05524C17.034 3.63895 16.5709 4.11224 15.9995 4.11234C15.4282 4.11244 14.965 3.63936 14.9649 3.05554C14.9648 2.47193 15.4279 1.99864 15.9991 1.99854Z"
      fill={color}
    />
  </svg>
);

const ThumbUp = ({ color }: { color?: string }) => (
  <svg className="icon thumbUp" viewBox="0 0 24 24" fill={color}>
    <path
      d="M11.4951 2.15479C13.058 2.1549 13.9932 3.10181 13.9932 4.68994L13.9902 4.7876L13.9854 4.91943C13.9854 6.17709 13.9823 7.70644 13.9795 8.53271L16.0498 8.59424H19.4609C20.6654 8.65866 21.8447 9.41248 21.8447 10.9419C21.8419 11.6112 21.671 12.0956 21.4609 12.4429C21.6738 12.7762 21.8418 13.2304 21.8418 13.8354C21.8417 14.4908 21.5792 15.099 21.0947 15.5835C21.2207 15.8775 21.3154 16.2616 21.3154 16.7515C21.3153 17.5384 20.9036 18.219 20.167 18.6812C20.237 19.0285 20.2319 19.4209 20.1562 19.8774C20.1506 19.9138 20.139 19.9533 20.125 19.9868C19.562 21.5131 18.6627 21.8432 15.0498 21.8433C12.7921 21.8433 11.8338 21.5633 11.0635 21.3364L10.8564 21.2778C10.5399 21.1854 10.2847 21.1128 10.125 21.1128L7.32129 21.0757C7.27501 21.0757 7.23121 21.0643 7.1875 21.0522C7.16787 21.0469 7.14785 21.041 7.12793 21.0366C6.86755 21.255 6.53994 21.3755 6.20117 21.3784H3.60938C2.80571 21.3783 2.15645 20.726 2.15625 19.9224V12.3257C2.15625 11.5219 2.80839 10.8688 3.60938 10.8687H6.20117C6.53432 10.8688 6.83955 10.9869 7.08594 11.1772C7.63489 10.8972 8.85279 10.135 8.9707 8.90283C9.07993 7.77973 9.06622 6.37614 9.04102 5.26416L9.03809 5.22217C9.03547 5.1253 9.02834 5.05242 9.02051 4.97705C9.01998 4.97196 9.01909 4.96656 9.01855 4.96143L8.99902 4.73193C8.9318 3.78235 9.27327 3.20165 9.56738 2.88232C10.01 2.4062 10.6773 2.15479 11.4951 2.15479ZM11.4951 3.38428C11.1674 3.38428 10.7245 3.44014 10.4668 3.72021C10.2763 3.9247 10.1946 4.23618 10.2227 4.64795L10.2402 4.83838L10.2412 4.85303C10.2491 4.93173 10.2574 5.00898 10.2627 5.10107C10.2655 5.12346 10.2676 5.14607 10.2676 5.16846C10.2928 6.32531 10.3102 7.80994 10.1953 9.02002C10.0216 10.8407 8.4134 11.8797 7.6543 12.269V12.2778C7.65431 12.2861 7.65585 12.2937 7.65723 12.3013C7.65863 12.309 7.66016 12.3173 7.66016 12.3257V19.8462L10.1367 19.8804C10.4643 19.8805 10.7895 19.9727 11.1982 20.0903L11.4141 20.1519L11.4248 20.1548C12.1532 20.3669 12.9821 20.6079 15.0527 20.6079C18.492 20.6079 18.7076 20.2747 18.9541 19.6108C19.0381 19.0648 18.9571 18.8207 18.873 18.6694C18.7109 18.3699 18.8226 17.998 19.1191 17.8354C19.1387 17.8242 19.1562 17.8155 19.1758 17.8071C19.5903 17.6362 20.0859 17.3085 20.0859 16.7456C20.0859 16.3506 19.9963 16.0393 19.8311 15.8433C19.7191 15.7117 19.6661 15.5355 19.6885 15.3647C19.7109 15.194 19.8055 15.0369 19.9482 14.936C20.3907 14.6392 20.6151 14.2667 20.6152 13.8354C20.6152 13.4349 20.4919 13.1211 20.251 12.897C20.0017 12.6645 19.9882 12.2722 20.2207 12.0229L20.2539 11.9888C20.4862 11.7759 20.6123 11.4121 20.6123 10.939C20.6123 9.98939 19.7804 9.84041 19.4219 9.8208H16.0303L13.3467 9.74268C13.0133 9.73427 12.7472 9.45967 12.75 9.12354C12.7501 9.10264 12.7578 6.7183 12.7578 4.90283V4.86865L12.7637 4.74854C12.7665 3.77385 12.3857 3.38436 11.4951 3.38428ZM3.60938 12.0952C3.48346 12.0954 3.38281 12.1997 3.38281 12.3257V19.9194C3.38295 20.0453 3.48633 20.1458 3.60938 20.146H6.20117C6.32141 20.1458 6.41921 20.0505 6.4248 19.9302V12.311C6.41906 12.1909 6.3213 12.0955 6.20117 12.0952H3.60938ZM4.88379 12.9448C5.33197 12.9448 5.64648 13.2584 5.64648 13.7065C5.64637 14.1546 5.33189 14.4683 4.88379 14.4683C4.4359 14.4681 4.12218 14.1545 4.12207 13.7065C4.12207 13.2557 4.43302 12.945 4.88379 12.9448Z"
      fill={color}
    />
  </svg>
);

const ThumbUpFilled = ({ color }: { color?: string }) => (
  <svg className="icon thumbUpFilled" viewBox="0 0 24 24" fill={color}>
    <path
      d="M11.4912 2.1543C13.0569 2.15436 13.9892 3.10415 13.9893 4.68945L13.9873 4.78711L13.9814 4.91895C13.9814 6.17934 13.9784 7.70592 13.9756 8.53223L16.0459 8.59375H19.457C20.6615 8.66098 21.8408 9.41208 21.8408 10.9414C21.8408 11.6135 21.6728 12.1008 21.46 12.4453C21.6728 12.7786 21.8409 13.2327 21.8438 13.832C21.8437 14.4902 21.583 15.0955 21.0957 15.5801C21.2217 15.8742 21.3174 16.258 21.3174 16.748C21.3173 17.535 20.9055 18.2156 20.1689 18.6777C20.239 19.025 20.2328 19.4175 20.1572 19.874C20.1488 19.9103 20.1409 19.9471 20.127 19.9834C19.5639 21.5098 18.6618 21.8398 15.0518 21.8398C12.8115 21.8398 11.8508 21.5613 11.083 21.3379L11.0684 21.333L10.8584 21.2744C10.5419 21.1792 10.2894 21.1094 10.127 21.1094L7.32031 21.0732C7.27438 21.0732 7.23201 21.0608 7.18945 21.0488C7.16986 21.0433 7.15015 21.0376 7.12988 21.0332C6.87778 21.2433 6.55509 21.375 6.20215 21.375H3.61133C2.80756 21.3749 2.15541 20.7227 2.15527 19.9189V12.3252C2.15534 11.5186 2.80752 10.8653 3.61133 10.8652H6.19727C6.53044 10.8653 6.83563 10.9834 7.08203 11.1738C7.63102 10.8909 8.84895 10.1317 8.9668 8.89941C9.07602 7.77629 9.06231 6.3727 9.03711 5.26074C9.03711 5.25377 9.03683 5.24622 9.03613 5.23926C9.03545 5.23245 9.0342 5.22556 9.03418 5.21875C9.03156 5.12464 9.02442 5.05182 9.0166 4.97656C9.01606 4.9714 9.01519 4.96614 9.01465 4.96094L8.99512 4.73145C8.92793 3.78194 9.26937 3.20115 9.56348 2.88184C10.0061 2.40572 10.6733 2.1543 11.4912 2.1543ZM4.88281 12.9463C4.43488 12.9465 4.12115 13.261 4.12109 13.709C4.12126 14.1568 4.43496 14.4705 4.88281 14.4707C5.33087 14.4707 5.64241 14.157 5.64258 13.709C5.64252 13.2581 5.33095 12.9463 4.88281 12.9463Z"
      fill={color}
    />
  </svg>
);

const ThumbDown = ({ color }: { color?: string }) => (
  <svg className="icon thumbDown" viewBox="0 0 24 24" fill={color}>
    <path
      d="M15.0479 2.1543C18.6613 2.1543 19.56 2.48499 20.123 4.00879C20.1371 4.0424 20.1487 4.08175 20.1543 4.11816C20.2299 4.57446 20.235 4.9663 20.165 5.31348C20.9017 5.77566 21.3135 6.45703 21.3135 7.24414C21.3134 7.73424 21.2188 8.11803 21.0928 8.41211C21.5773 8.89669 21.8408 9.50474 21.8408 10.1602C21.8408 10.7648 21.6727 11.2185 21.46 11.5518C21.67 11.8991 21.8409 12.3842 21.8438 13.0537C21.8436 14.5828 20.6642 15.3358 19.46 15.4004H16.0479L13.9775 15.4629C13.9803 16.2893 13.9834 17.8187 13.9834 19.0762L13.9893 19.207L13.9922 19.3057C13.9921 20.8938 13.0562 21.8408 11.4932 21.8408C10.6754 21.8408 10.009 21.5884 9.56641 21.1123C9.2723 20.793 8.92989 20.2132 8.99707 19.2637L9.0166 19.0342L9.01855 19.0186C9.0264 18.9431 9.03449 18.8704 9.03711 18.7734L9.03906 18.7314C9.06427 17.6194 9.07896 16.216 8.96973 15.0928C8.85208 13.8603 7.633 13.0985 7.08398 12.8184C6.83756 13.0087 6.53239 13.126 6.19922 13.126H3.6084C2.80735 13.126 2.15442 12.4737 2.1543 11.6699V4.07617C2.1543 3.27225 2.80728 2.61914 3.6084 2.61914H6.19922C6.53802 2.62196 6.86553 2.74259 7.12598 2.96094C7.14588 2.95661 7.16593 2.95168 7.18555 2.94629C7.22934 2.93425 7.27298 2.92191 7.31934 2.92188L10.123 2.88574C10.2827 2.88574 10.538 2.81216 10.8545 2.71973L11.0615 2.66113C11.8318 2.43424 12.7902 2.1543 15.0479 2.1543ZM15.0479 3.38672C12.9771 3.38672 12.1483 3.62773 11.4199 3.83984L11.4092 3.84375L11.1934 3.90527C10.7845 4.02289 10.4595 4.11523 10.1318 4.11523L7.65527 4.14844V11.6699C7.65525 11.6782 7.65469 11.6858 7.65332 11.6934C7.65192 11.7011 7.65039 11.7094 7.65039 11.7178V11.7256C8.40951 12.115 10.0166 13.1543 10.1904 14.9746C10.3053 16.1846 10.2889 17.6694 10.2637 18.8262C10.2637 18.8485 10.2606 18.8712 10.2578 18.8936C10.2525 18.9858 10.2452 19.0638 10.2373 19.1426L10.2354 19.1572L10.2188 19.3477C10.1908 19.7594 10.2724 20.0699 10.4629 20.2744C10.7206 20.5544 11.1625 20.6113 11.4902 20.6113C12.3809 20.6113 12.7625 20.2216 12.7598 19.2471L12.7539 19.126C12.7483 17.7087 12.7451 16.2884 12.7451 14.8711C12.7425 14.5351 13.0086 14.2604 13.3418 14.252L16.0283 14.1738H19.4199C19.7784 14.1542 20.6111 14.0059 20.6113 13.0566C20.6113 12.5833 20.4844 12.2187 20.252 12.0059L20.2188 11.9727C19.9863 11.7234 20.0007 11.3311 20.25 11.0986C20.4908 10.8746 20.6139 10.5606 20.6084 10.1631C20.6084 9.73451 20.3841 9.36111 19.9443 9.05859C19.8017 8.95782 19.7061 8.80154 19.6836 8.63086C19.6612 8.46004 19.7142 8.28301 19.8262 8.15137C19.9914 7.95537 20.081 7.64474 20.0811 7.25C20.0811 6.68697 19.5855 6.35837 19.1709 6.1875C19.1515 6.17918 19.1346 6.17123 19.1152 6.16016C18.8184 5.99773 18.7059 5.62488 18.8682 5.3252C18.9522 5.17393 19.0342 4.93001 18.9502 4.38379C18.7037 3.71996 18.4875 3.38672 15.0479 3.38672ZM3.6084 3.85156C3.48515 3.85156 3.38086 3.95305 3.38086 4.0791V11.6699C3.38098 11.7959 3.48242 11.8994 3.6084 11.8994H6.19922C6.31962 11.8994 6.41725 11.804 6.42285 11.6836V4.06445C6.41434 3.94694 6.31954 3.84869 6.19922 3.84863L3.6084 3.85156ZM4.88281 9.52637C5.33087 9.52647 5.64453 9.84096 5.64453 10.2891C5.64439 10.737 5.33077 11.0507 4.88281 11.0508C4.43194 11.0508 4.12124 10.7399 4.12109 10.2891C4.12109 9.84088 4.43463 9.52637 4.88281 9.52637Z"
      fill={color}
    />{" "}
  </svg>
);

const ThumbDownFilled = ({ color }: { color?: string }) => (
  <svg className="icon thumbDownFilled" viewBox="0 0 24 24" fill={color}>
    <path
      d="M15.0518 2.1543C18.6617 2.15433 19.5649 2.48449 20.1279 4.01074C20.1419 4.04701 20.1498 4.08385 20.1582 4.12012C20.2338 4.57655 20.2399 4.96915 20.1699 5.31641C20.9062 5.77849 21.3182 6.45932 21.3184 7.24609C21.3184 7.73613 21.2227 8.12096 21.0967 8.41504C21.5838 8.89947 21.8446 9.50416 21.8447 10.1621C21.8419 10.7642 21.6737 11.2185 21.4609 11.5518C21.6737 11.8962 21.8417 12.3837 21.8418 13.0557C21.8418 14.5849 20.6623 15.3359 19.458 15.4033H16.0469L13.9766 15.4648C13.9794 16.2911 13.9824 17.8177 13.9824 19.0781L13.9873 19.21L13.9902 19.3086C13.99 20.8935 13.0574 21.8425 11.4922 21.8428C10.6744 21.8428 10.007 21.5913 9.56445 21.1152C9.27034 20.7959 8.92887 20.2152 8.99609 19.2656L9.01562 19.0361L9.01758 19.0205C9.0254 18.9453 9.03254 18.8724 9.03516 18.7783C9.0352 18.7715 9.03643 18.7646 9.03711 18.7578C9.0378 18.7509 9.03809 18.7433 9.03809 18.7363C9.06329 17.6244 9.077 16.2207 8.96777 15.0977C8.84988 13.8657 7.63212 13.1063 7.08301 12.8232C6.83674 13.0135 6.53121 13.1316 6.19824 13.1318H3.6123C2.8084 13.1318 2.15527 12.4786 2.15527 11.6719V4.0752C2.15557 3.27154 2.80858 2.61916 3.6123 2.61914H6.20312C6.55588 2.61926 6.87887 2.75095 7.13086 2.96094C7.15096 2.95653 7.17098 2.95076 7.19043 2.94531C7.23283 2.93344 7.27553 2.92188 7.32129 2.92188L10.1279 2.88477C10.2904 2.88457 10.5425 2.81478 10.8584 2.71973L11.0693 2.66113L11.084 2.65723C11.8517 2.43388 12.8121 2.15433 15.0518 2.1543ZM4.88379 9.52637C4.43593 9.52649 4.12232 9.84028 4.12207 10.2881C4.12207 10.7362 4.43575 11.0507 4.88379 11.0508C5.33171 11.0506 5.64355 10.7389 5.64355 10.2881C5.6433 9.84036 5.33153 9.5266 4.88379 9.52637Z"
      fill={color}
    />
  </svg>
);

const AddMore = ({ color }: { color?: string }) => (
  <svg className="icon addMore" viewBox="0 0 24 24" fill={color}>
    <path
      d="M11.9962 3.0304C12.5 3.0304 12.8007 3.43247 12.8009 3.82727V11.1222H20.1691C20.6171 11.1478 20.9733 11.5033 20.9992 11.9513C21.0248 12.4327 20.6559 12.8483 20.172 12.8741H12.8751V20.171C12.8519 20.6163 12.4933 20.9749 12.048 20.9982C11.5666 21.0238 11.1509 20.6549 11.1251 20.171V12.8058H3.82825C3.38295 12.7826 3.02436 12.4923 3.0011 12.047C2.9755 11.5656 3.34439 11.2154 3.82825 11.1896H11.1945V3.82727C11.1947 3.42856 11.5159 3.0304 11.9962 3.0304Z"
      fill={color}
    />
  </svg>
);

const Close = ({ color }: { color?: string }) => (
  <svg className="icon close" viewBox="0 0 24 24" fill={color}>
    <path
      d="M16.7666 6.21074C17.0486 5.9287 17.506 5.92879 17.7881 6.21074C18.0465 6.46922 18.0687 6.8749 17.8535 7.158L17.7881 7.23222L13.0215 11.9988L17.789 16.7654C18.071 17.0474 18.0708 17.5048 17.789 17.7869C17.5305 18.0455 17.1239 18.0678 16.8408 17.8523L16.7675 17.7869L12 13.0203L7.23337 17.7879C6.95141 18.0698 6.49399 18.0696 6.21188 17.7879C5.95331 17.5293 5.93098 17.1228 6.14645 16.8396L6.21188 16.7664L10.9785 11.9988L6.21188 7.23222C5.92987 6.95021 5.93 6.49283 6.21188 6.21074C6.47034 5.95229 6.87605 5.93015 7.15915 6.14531L7.23337 6.21074L12 10.9773L16.7666 6.21074Z"
      fill={color}
    />
  </svg>
);

const Back = ({ color }: { color?: string }) => (
  <svg className="icon back" viewBox="0 0 24 24" fill={color}>
    <path
      d="M13.3022 4.27266C13.7026 3.89234 14.3428 3.91239 14.7231 4.3127C15.1035 4.71306 15.0835 5.35326 14.6831 5.7336L8.02783 11.9992L14.6831 18.2648C15.0935 18.6452 15.1035 19.2854 14.7231 19.6857C14.3428 20.0861 13.7026 20.1061 13.3022 19.7258L5.86572 12.7297C5.66559 12.5395 5.54541 12.2694 5.54541 11.9992C5.54541 11.719 5.65558 11.4589 5.86572 11.2688L13.3022 4.27266Z"
      fill={color}
    />
  </svg>
);

const NextIcon = ({ color }: { color?: string }) => (
  <svg className="icon next" viewBox="0 0 24 24" fill={color}>
    <path
      d="M9.27648 4.31223C9.65682 3.91188 10.298 3.89186 10.6984 4.2722L18.1349 11.2683C18.335 11.4584 18.4552 11.7286 18.4552 11.9988C18.4552 12.2789 18.345 12.5391 18.1349 12.7292L10.6984 19.7253C10.298 20.1057 9.65682 20.0856 9.27648 19.6853C8.89658 19.285 8.91654 18.6446 9.31652 18.2644L15.9728 11.9988L9.31652 5.73313C8.90655 5.35288 8.89657 4.71252 9.27648 4.31223Z"
      fill={color}
    />
  </svg>
);

const Star = ({ color }: { color?: string }) => (
  <svg className="icon star" viewBox="0 0 24 24" fill={color}>
    <path
      d="M12.0015 2.172C12.825 2.172 13.5649 2.64557 13.9263 3.41028L15.7886 7.34583C15.7886 7.34846 15.7888 7.34894 15.7915 7.35168C15.8 7.36836 15.817 7.3821 15.8364 7.38489L20.0015 8.01477C20.7969 8.13521 21.4558 8.69835 21.7163 9.48254C21.9852 10.2949 21.7858 11.1723 21.1919 11.7745L18.1782 14.8361C18.1559 14.8585 18.1473 14.8894 18.1529 14.923L18.8648 19.2501C19.0048 20.1072 18.6599 20.9475 17.9654 21.4406C17.6012 21.6982 17.1782 21.8273 16.7525 21.8273C16.4079 21.8273 16.0631 21.7402 15.7466 21.5665L12.0181 19.5245C12.007 19.5192 11.993 19.519 11.982 19.5245L8.25344 21.5665C7.54762 21.953 6.6968 21.9053 6.0386 21.4376C5.34112 20.9446 4.99633 20.1043 5.13918 19.2472L5.85012 14.923C5.85569 14.8895 5.84513 14.8584 5.82571 14.8361L2.81106 11.7745C2.21722 11.1723 2.01884 10.2949 2.29055 9.48254C2.55113 8.69848 3.20909 8.13816 4.00442 8.0177L8.16946 7.38782C8.18906 7.38502 8.20597 7.37044 8.21438 7.35364V7.35168C8.21438 7.35168 8.21582 7.35028 8.21731 7.34875L10.0806 3.41321C10.4391 2.64578 11.1781 2.1721 12.0015 2.172ZM12.0015 3.26184C11.5954 3.26194 11.245 3.49158 11.063 3.87805L9.20071 7.81067C9.03824 8.15801 8.7185 8.39846 8.34035 8.46008H8.33547L4.1675 9.09094C3.77262 9.14975 3.45545 9.42391 3.32375 9.82434C3.21451 10.1549 3.22621 10.6401 3.59035 11.0099L6.60403 14.0743L6.60696 14.0763C6.87019 14.3451 6.98781 14.7292 6.92629 15.1017L6.21438 19.4269C6.12757 19.9563 6.39118 20.3568 6.66848 20.5529C6.82255 20.6621 7.23435 20.8861 7.73293 20.6144L11.4614 18.5724C11.6294 18.4801 11.814 18.4328 12.0015 18.4327C12.1892 18.4327 12.3746 18.4799 12.5454 18.5724L16.271 20.6144C16.7667 20.8859 17.1814 20.6621 17.3355 20.5529C17.6127 20.3567 17.8754 19.9589 17.7886 19.4269L17.0777 15.1017C17.0161 14.732 17.1337 14.3481 17.397 14.0792L17.3999 14.0763L20.4165 11.0118C20.7803 10.6421 20.7914 10.1577 20.6822 9.82727C20.5477 9.42689 20.2342 9.15273 19.8394 9.09387L15.6714 8.46008H15.6655C15.2874 8.39843 14.9648 8.15797 14.8052 7.81067L12.9429 3.87805C12.7608 3.4915 12.4076 3.26184 12.0015 3.26184Z"
      fill={color}
    />
  </svg>
);

const StarFilled = ({ color }: { color?: string }) => (
  <svg className="icon starFilled" viewBox="0 0 24 24" fill={color}>
    <path
      d="M11.9949 2.172C12.8212 2.172 13.5584 2.64837 13.9197 3.41028L15.6502 7.06555C15.7902 7.32876 15.8354 7.38793 16.1404 7.43274L19.9978 8.0177C20.7933 8.13814 21.4494 8.7012 21.7127 9.48547C21.9844 10.2866 21.7822 11.1752 21.1912 11.7775L18.4793 14.5079C18.1685 14.8187 18.1574 14.8475 18.2078 15.2228L18.8582 19.2531C18.9982 20.1101 18.6534 20.9505 17.9588 21.4435C17.6059 21.6955 17.1829 21.8331 16.7488 21.8331C16.3959 21.8331 16.0511 21.7433 15.743 21.5724L12.1824 19.6202C12.0453 19.5559 11.9309 19.5395 11.7937 19.629L8.24686 21.5724C7.54389 21.9588 6.68458 21.9111 6.03202 21.4435C5.33454 20.9505 4.99255 20.1102 5.1326 19.2531L5.79667 15.2247C5.86092 14.9087 5.80734 14.8327 5.61698 14.6339L2.80448 11.7775C2.21064 11.1724 2.00946 10.2866 2.28397 9.48547C2.54454 8.70135 3.20246 8.13815 3.99784 8.0177L7.89432 7.42688C8.13239 7.38206 8.2029 7.34853 8.35135 7.04602L10.074 3.41028C10.4325 2.64565 11.1687 2.17208 11.9949 2.172Z"
      fill={color}
    />
  </svg>
);

const CheckCircle = ({ color }: { color?: string }) => (
  <svg className="icon checkCircle" viewBox="0 0 24 24" fill={color}>
    <path
      d="M11.9971 0.999023C15.4921 0.999059 18.2098 1.91105 20.0781 3.70996C22.0186 5.57803 23 8.36827 23 12.001C22.9999 15.6335 22.0154 18.4201 20.0781 20.2881C18.2098 22.087 15.4921 22.999 11.9971 22.999C8.502 22.999 5.78414 22.087 3.91895 20.2881C1.98167 18.4232 1.00003 15.6365 1 12.0039C1 8.37119 1.98164 5.58096 3.91895 3.71289C5.78414 1.91408 8.5052 0.999023 11.9971 0.999023ZM11.9971 2.21875C5.50804 2.21875 2.21582 5.50953 2.21582 12.0039C2.21588 18.4919 5.5081 21.7832 11.9971 21.7832C18.489 21.7831 21.7841 18.4918 21.7842 12.0039C21.7842 5.51274 18.4891 2.21882 11.9971 2.21875ZM16.3916 7.2959C16.5828 7.04211 16.9434 6.98874 17.1973 7.17969C17.4512 7.37086 17.5046 7.73147 17.3135 7.98535L10.8623 16.624C10.7558 16.768 10.5896 16.8522 10.4111 16.8555H10.3984C10.2229 16.8555 10.056 16.7776 9.94629 16.6396L6.99414 12.9287C6.79665 12.6811 6.83732 12.3176 7.08496 12.1201C7.33261 11.9227 7.69606 11.9633 7.89355 12.2109L10.3828 15.3389L16.3916 7.2959Z"
      fill={color}
    />
  </svg>
);
